/* Copyright(c) 2009-2025 shenzhen TP-Link Technologies Co.Ltd. *  
* file        server.c
* brief       TCP communication - server.
*
* author      <PERSON>
* version     1.0.0
* date        07August25
*
* history     \arg 1.0.0, 07<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Create the file.
*
*/
 
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <pthread.h>

/************************************************************************************************/
/*                                               DEFINES                                        */
/************************************************************************************************/

#define PORT 8080          /*Communication port*/
#define BUFFER_SIZE 1024   /*Message buffer size*/

/************************************************************************************************/
/*                                               TYPES                                          */
/************************************************************************************************/

/************************************************************************************************/
/*                                               EXTERN_PROTOTYPES                              */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_PROTOTYPES                               */
/************************************************************************************************/

/************************************************************************************************/
/*                                               VARIABLES                                      */
/************************************************************************************************/

int client_socket;     /*Client socket descriptor*/

/************************************************************************************************/
/*                                               LOCAL_FUNCTIONS                                */
/************************************************************************************************/

/************************************************************************************************/
/*                                               PUBLIC_FUNCTIONS                               */
/************************************************************************************************/

/*
 *fn           void *receive_handler(void *arg)
 *brief        Thread function to handle incoming messages continuously receives messages from 
               client and prints them.
 *
 * param[in]   arg    A positive integer
 * 
 */
void *receive_handler(void *arg){
    char buffer[BUFFER_SIZE];
    while(1){
        int bytes_received = recv(client_socket, buffer, BUFFER_SIZE, 0);
        if (bytes_received <=0){
            perror("connection closed");
            exit(EXIT_FAILURE);
        }
        buffer[bytes_received] = '\0';
        printf("client: %s", buffer);
    }
    return NULL;
}

/************************************************************************************************/
/*                                               GLOBAL_FUNCTIONS                               */
/************************************************************************************************/

int main(){
    int server_socket;
    struct sockaddr_in server_addr,client_addr;
    socklen_t addr_len = sizeof(client_addr);
    
    /*Create server socket*/
    if((server_socket = socket(AF_INET, SOCK_STREAM, 0)) < 0){
        perror("socket creation failed");
        exit(EXIT_FAILURE);
    }
    
    /*Configure server address*/
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(PORT);

    /*Bind socket to address*/
    if(bind(server_socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0){
        perror("bind failed");
        exit(EXIT_FAILURE);
    }

    /*Listen for incoming connections*/
    if(listen(server_socket, 1) < 0){
        perror("listen failed");
    }

    printf("server listening on port %d\n", PORT);

    /*Accept client connection*/
    if((client_socket = accept(server_socket, (struct sockaddr*)&client_addr, &addr_len))<0){
        perror("accept failed");
        exit(EXIT_FAILURE);
    }

    printf("client connected\n");

    /*Create receive thread*/
    pthread_t recv_thread;
    pthread_create(&recv_thread, NULL, receive_handler, NULL);

    /*Main thread: message sending loop*/
    char message[BUFFER_SIZE];
    while(1){
        fgets(message, BUFFER_SIZE, stdin);
        if(send(client_socket, message, strlen(message),0) < 0){
            perror("send failed");
            break;
        }
    }

    /*Cleanup resources*/
    close(client_socket);
    close(server_socket);
    return 0;
}