/* Copyright(c) 2009-2025 shenzhen TP-Link Technologies Co.Ltd. *  
* file        task2.c
* brief       Implement a simple shell.
*
* author      <PERSON>
* version     1.0.0
* date        06August25
*
* history     \arg 1.0.0, 06<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Create the file.
*
*/
 
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <sys/wait.h>
#include <sys/types.h>
#include <fcntl.h>
#include <errno.h>
#include <sys/select.h>
 
/************************************************************************************************/
/*                                               DEFINES                                        */
/************************************************************************************************/

#define MAX_INPUT 1024
#define MAX_ARGS 64
 
/************************************************************************************************/
/*                                               TYPES                                          */
/************************************************************************************************/

/************************************************************************************************/
/*                                               EXTERN_PROTOTYPES                              */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_PROTOTYPES                               */
/************************************************************************************************/

/************************************************************************************************/
/*                                               VARIABLES                                      */
/************************************************************************************************/

/************************************************************************************************/
/*                                               LOCAL_FUNCTIONS                                */
/************************************************************************************************/

/************************************************************************************************/
/*                                               PUBLIC_FUNCTIONS                               */
/************************************************************************************************/

/************************************************************************************************/
/*                                               GLOBAL_FUNCTIONS                               */
/************************************************************************************************/

int main()
{
    char input[MAX_INPUT];
    char *args[MAX_ARGS];
    char *token;
    pid_t pid;
    int status;
 
    while(1) {
        printf("myshell> ");
        fflush(stdout);

        /*If fgets returns NULL, check whether it is an EAGAIN error*/
        if (fgets(input, sizeof(input), stdin) == NULL) {
            if (feof(stdin)) {
                break; 
            } else if (errno == EAGAIN) {
                clearerr(stdin);
                continue;
            } else {
                break;
            }
        }
 
        input[strcspn(input, "\n")] = '\0';
 
        if (strcmp(input, "exit") == 0) {
            printf("shell exiting...\n");
            break;
        }
 
        if(strlen(input) == 0) {
            continue;
        }
 
        /*Handling of the "stop" command*/
        if (strcmp(input, "stop") == 0) {
            printf("No command is currently running.\n");
            continue;
        }
 
        int i = 0;
        token = strtok(input, " ");
        while(token != NULL && i < MAX_ARGS - 1) {
            args[i] = token;
            i++;
            token = strtok(NULL, " ");
        }
        args[i] = NULL;

        int pipefd[2];
        if (pipe(pipefd) == -1) {
            perror("pipe failed");
            continue;
        }
 
        pid = fork();
        if(pid < 0) {
            perror("fork failed");
            close(pipefd[0]);
            close(pipefd[1]);
            continue;
        }

        /*Child process*/
        else if(pid == 0) {  
            close(pipefd[0]);  
            
            if (dup2(pipefd[1], STDOUT_FILENO) == -1) {
                perror("dup2 failed");
                exit(1);
            }
            close(pipefd[1]);
            
            /*Execute the input command*/
            execvp(args[0], args);
            perror("execvp failed");
            exit(1);
        }

        /*Parent process*/
        else { 
            int stdin_flags = fcntl(STDIN_FILENO, F_GETFL);
            fcntl(STDIN_FILENO, F_SETFL, stdin_flags | O_NONBLOCK);
            close(pipefd[1]);  
            
            char buffer[1024];
            ssize_t bytes_read;
            int stop_detected = 0;
            memset(buffer, 0, sizeof(buffer));
            
            while(1) {
                fd_set readfds;
                FD_ZERO(&readfds);
                FD_SET(pipefd[0], &readfds);
                FD_SET(STDIN_FILENO, &readfds);
                
                int max_fd = (pipefd[0] > STDIN_FILENO) ? pipefd[0] : STDIN_FILENO;
                
                /*Set the timeout period to 100ms*/
                struct timeval tv = {0, 100000};
                int activity = select(max_fd + 1, &readfds, NULL, NULL, &tv);
                
                if (activity < 0) {
                    perror("select error");
                    break;
                }
                
                /*Check the output of the child process*/
                if (FD_ISSET(pipefd[0], &readfds)) {
                    bytes_read = read(pipefd[0], buffer, sizeof(buffer) - 1);
                    if (bytes_read > 0) {
                        buffer[bytes_read] = '\0';
                        printf("%s", buffer);
                        fflush(stdout);
                        if (strstr(buffer, "stop") != NULL) {
                            stop_detected = 1;
                            break;
                        }
                    }
                    else if (bytes_read == 0) {
                        break;
                    }
                    memset(buffer, 0, sizeof(buffer));
                }
                
                /*Check the user input*/
                if (FD_ISSET(STDIN_FILENO, &readfds)) {
                    char user_cmd[32];
                    if (fgets(user_cmd, sizeof(user_cmd), stdin) != NULL) {
                        user_cmd[strcspn(user_cmd, "\n")] = '\0';
                        if (strcmp(user_cmd, "stop") == 0) {
                            stop_detected = 1;
                            break;
                        }
                    }
                }
                
                /*Check whether the child process has exited*/
                if (waitpid(pid, &status, WNOHANG) == pid) {
                    break;
                }
            }
            
            close(pipefd[0]);  
            fcntl(STDIN_FILENO, F_SETFL, stdin_flags);
            
            if (stop_detected) {
                kill(pid, SIGTERM);
                printf("\nCommand terminated by 'stop' signal\n");
            }
            
            /*Ensure that the child process exits*/
            waitpid(pid, &status, 0);
        }
    }
    
    return 0;
}