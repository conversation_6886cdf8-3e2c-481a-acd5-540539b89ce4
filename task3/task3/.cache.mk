__cached_gcc_-v_2>&1_|_grep_-q_"clang_version"_&&_echo_clang_||_echo_gcc := gcc
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-mretpoline-external-thunk_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-mretpoline-external-thunk";_else_echo_"";_fi := 
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-mindirect-branch_thunk-extern_-mindirect-branch-register_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-mindirect-branch_thunk-extern_-mindirect-branch-register";_else_echo_"";_fi := -mindirect-branch=thunk-extern -mindirect-branch-register
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-mretpoline_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-mretpoline";_else_echo_"";_fi := 
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-mindirect-branch_thunk-inline_-mindirect-branch-register_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-mindirect-branch_thunk-inline_-mindirect-branch-register";_else_echo_"";_fi := -mindirect-branch=thunk-inline -mindirect-branch-register
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-fno-PIE";_else_echo_"";_fi := -fno-PIE
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-fno-PIE_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-fno-PIE";_else_echo_"";_fi := -fno-PIE
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-Wmaybe-uninitialized_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-maybe-uninitialized";_else_echo_"";_fi := -Wno-maybe-uninitialized
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE__-mpreferred-stack-boundary_4_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"_-mpreferred-stack-boundary_4";_else_echo_"";_fi := -mpreferred-stack-boundary=4
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE__-m16_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"_-m16";_else_echo_"_-m32_-Wa_./arch/x86/boot/code16gcc.h";_fi := -m16
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-fcf-protection_none_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-fcf-protection_none";_else_echo_"";_fi := 
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-m16_-g_-Os_-D__KERNEL___-DDISABLE_BRANCH_PROFILING_-Wall_-Wstrict-prototypes_-march_i386_-mregparm_3_-fno-strict-aliasing_-fomit-frame-pointer_-fno-pic_-mno-mmx_-mno-sse___-ffreestanding_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"_-ffreestanding";_else_echo_"";_fi := -ffreestanding
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-m16_-g_-Os_-D__KERNEL___-DDISABLE_BRANCH_PROFILING_-Wall_-Wstrict-prototypes_-march_i386_-mregparm_3_-fno-strict-aliasing_-fomit-frame-pointer_-fno-pic_-mno-mmx_-mno-sse__-ffreestanding__-fno-stack-protector_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"_-fno-stack-protector";_else_echo_"";_fi := -fno-stack-protector
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-m16_-g_-Os_-D__KERNEL___-DDISABLE_BRANCH_PROFILING_-Wall_-Wstrict-prototypes_-march_i386_-mregparm_3_-fno-strict-aliasing_-fomit-frame-pointer_-fno-pic_-mno-mmx_-mno-sse__-ffreestanding_-fno-stack-protector__-Wno-address-of-packed-member_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"_-Wno-address-of-packed-member";_else_echo_"";_fi := -Wno-address-of-packed-member
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-m16_-g_-Os_-D__KERNEL___-DDISABLE_BRANCH_PROFILING_-Wall_-Wstrict-prototypes_-march_i386_-mregparm_3_-fno-strict-aliasing_-fomit-frame-pointer_-fno-pic_-mno-mmx_-mno-sse__-ffreestanding_-fno-stack-protector_-Wno-address-of-packed-member__-mpreferred-stack-boundary_2_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"_-mpreferred-stack-boundary_2";_else_echo_"";_fi := -mpreferred-stack-boundary=2
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-mno-avx";_else_echo_"";_fi := -mno-avx
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-fcf-protection_none_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-fcf-protection_none";_else_echo_"";_fi := 
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-falign-jumps_1";_else_echo_"";_fi := -falign-jumps=1
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-falign-loops_1";_else_echo_"";_fi := -falign-loops=1
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-mno-80387";_else_echo_"";_fi := -mno-80387
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-mno-fp-ret-in-387";_else_echo_"";_fi := -mno-fp-ret-in-387
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-mpreferred-stack-boundary_3";_else_echo_"";_fi := -mpreferred-stack-boundary=3
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-mskip-rax-setup";_else_echo_"";_fi := -mskip-rax-setup
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-mtune_generic";_else_echo_"";_fi := -mtune=generic
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-funit-at-a-time";_else_echo_"";_fi := -funit-at-a-time
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI__-mfentry_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"y";_else_echo_"n";_fi := y
__cached_/bin/bash_./scripts/gcc-version.sh_-p_gcc := 070500
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___printf_"%b_n"_".cfi_startproc_n.cfi_rel_offset_rsp_0_n.cfi_endproc"_|_gcc_-D__ASSEMBLY___-fno-PIE_-m64_-DCONFIG_X86_X32_ABI_-c_-x_assembler_-o_"_TMP"_-__>/dev/null_2>&1;_then_echo_"-DCONFIG_AS_CFI_1";_else_echo_"";_fi := -DCONFIG_AS_CFI=1
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___printf_"%b_n"_".cfi_startproc_n.cfi_signal_frame_n.cfi_endproc"_|_gcc_-D__ASSEMBLY___-fno-PIE_-m64_-DCONFIG_X86_X32_ABI_-c_-x_assembler_-o_"_TMP"_-__>/dev/null_2>&1;_then_echo_"-DCONFIG_AS_CFI_SIGNAL_FRAME_1";_else_echo_"";_fi := -DCONFIG_AS_CFI_SIGNAL_FRAME=1
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___printf_"%b_n"_".cfi_sections_.debug_frame"_|_gcc_-D__ASSEMBLY___-fno-PIE_-m64_-DCONFIG_X86_X32_ABI_-c_-x_assembler_-o_"_TMP"_-__>/dev/null_2>&1;_then_echo_"-DCONFIG_AS_CFI_SECTIONS_1";_else_echo_"";_fi := -DCONFIG_AS_CFI_SECTIONS=1
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___printf_"%b_n"_"fxsaveq__%rax_"_|_gcc_-D__ASSEMBLY___-fno-PIE_-m64_-DCONFIG_X86_X32_ABI_-c_-x_assembler_-o_"_TMP"_-__>/dev/null_2>&1;_then_echo_"-DCONFIG_AS_FXSAVEQ_1";_else_echo_"";_fi := -DCONFIG_AS_FXSAVEQ=1
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___printf_"%b_n"_"pshufb_%xmm0_%xmm0"_|_gcc_-D__ASSEMBLY___-fno-PIE_-m64_-DCONFIG_X86_X32_ABI_-c_-x_assembler_-o_"_TMP"_-__>/dev/null_2>&1;_then_echo_"-DCONFIG_AS_SSSE3_1";_else_echo_"";_fi := -DCONFIG_AS_SSSE3=1
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___printf_"%b_n"_"crc32l_%eax_%eax"_|_gcc_-D__ASSEMBLY___-fno-PIE_-m64_-DCONFIG_X86_X32_ABI_-c_-x_assembler_-o_"_TMP"_-__>/dev/null_2>&1;_then_echo_"-DCONFIG_AS_CRC32_1";_else_echo_"";_fi := -DCONFIG_AS_CRC32=1
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___printf_"%b_n"_"vxorps_%ymm0_%ymm1_%ymm2"_|_gcc_-D__ASSEMBLY___-fno-PIE_-m64_-DCONFIG_X86_X32_ABI_-c_-x_assembler_-o_"_TMP"_-__>/dev/null_2>&1;_then_echo_"-DCONFIG_AS_AVX_1";_else_echo_"";_fi := -DCONFIG_AS_AVX=1
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___printf_"%b_n"_"vpbroadcastb_%xmm0_%ymm1"_|_gcc_-D__ASSEMBLY___-fno-PIE_-m64_-DCONFIG_X86_X32_ABI_-c_-x_assembler_-o_"_TMP"_-__>/dev/null_2>&1;_then_echo_"-DCONFIG_AS_AVX2_1";_else_echo_"";_fi := -DCONFIG_AS_AVX2=1
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___printf_"%b_n"_"vpmovm2b_%k1_%zmm5"_|_gcc_-D__ASSEMBLY___-fno-PIE_-m64_-DCONFIG_X86_X32_ABI_-c_-x_assembler_-o_"_TMP"_-__>/dev/null_2>&1;_then_echo_"-DCONFIG_AS_AVX512_1";_else_echo_"";_fi := -DCONFIG_AS_AVX512=1
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___printf_"%b_n"_"sha1msg1_%xmm0_%xmm1"_|_gcc_-D__ASSEMBLY___-fno-PIE_-m64_-DCONFIG_X86_X32_ABI_-c_-x_assembler_-o_"_TMP"_-__>/dev/null_2>&1;_then_echo_"-DCONFIG_AS_SHA1_NI_1";_else_echo_"";_fi := -DCONFIG_AS_SHA1_NI=1
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___printf_"%b_n"_"sha256msg1_%xmm0_%xmm1"_|_gcc_-D__ASSEMBLY___-fno-PIE_-m64_-DCONFIG_X86_X32_ABI_-c_-x_assembler_-o_"_TMP"_-__>/dev/null_2>&1;_then_echo_"-DCONFIG_AS_SHA256_NI_1";_else_echo_"";_fi := -DCONFIG_AS_SHA256_NI=1
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___ld_-m_elf_x86_64__-z_max-page-size_0x200000_-v__>/dev/null_2>&1;_then_echo_"_-z_max-page-size_0x200000";_else_echo_"";_fi := -z max-page-size=0x200000
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-fno-jump-tables";_else_echo_"";_fi := -fno-jump-tables
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-fno-delete-null-pointer-checks";_else_echo_"";_fi := -fno-delete-null-pointer-checks
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wframe-address_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-frame-address";_else_echo_"";_fi := -Wno-frame-address
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wformat-truncation_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-format-truncation";_else_echo_"";_fi := -Wno-format-truncation
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wformat-overflow_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-format-overflow";_else_echo_"";_fi := -Wno-format-overflow
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wint-in-bool-context_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-int-in-bool-context";_else_echo_"";_fi := -Wno-int-in-bool-context
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-Waddress-of-packed-member_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-address-of-packed-member";_else_echo_"";_fi := 
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-Wattribute-alias_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-attribute-alias";_else_echo_"";_fi := 
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"--param_allow-store-data-races_0";_else_echo_"";_fi := --param=allow-store-data-races=0
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-fno-allow-store-data-races_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-fno-allow-store-data-races";_else_echo_"";_fi := 
__cached_/bin/bash_./scripts/gcc-goto.sh_gcc_-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx__-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context___-O2_--param_allow-store-data-races_0_ := y
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wframe-larger-than_1024";_else_echo_"";_fi := -Wframe-larger-than=1024
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wunused-but-set-variable_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-unused-but-set-variable";_else_echo_"";_fi := -Wno-unused-but-set-variable
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wunused-const-variable_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-unused-const-variable";_else_echo_"";_fi := -Wno-unused-const-variable
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls__-fno-var-tracking-assignments_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"_-fno-var-tracking-assignments";_else_echo_"";_fi := -fno-var-tracking-assignments
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g__-gdwarf-4_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"_-gdwarf-4";_else_echo_"";_fi := -gdwarf-4
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-mrecord-mcount_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"y";_else_echo_"n";_fi := y
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4__-mfentry_-DCC_USING_FENTRY_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"_-mfentry_-DCC_USING_FENTRY";_else_echo_"";_fi := -mfentry -DCC_USING_FENTRY
__cached_gcc_-print-file-name_include := /usr/lib/gcc/x86_64-linux-gnu/7/include
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wdeclaration-after-statement";_else_echo_"";_fi := -Wdeclaration-after-statement
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wpointer-sign_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-pointer-sign";_else_echo_"";_fi := -Wno-pointer-sign
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wstringop-truncation_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-stringop-truncation";_else_echo_"";_fi := 
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wzero-length-bounds_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-zero-length-bounds";_else_echo_"";_fi := 
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Warray-bounds_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-array-bounds";_else_echo_"";_fi := -Wno-array-bounds
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wstringop-overflow_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-stringop-overflow";_else_echo_"";_fi := -Wno-stringop-overflow
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wrestrict_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-restrict";_else_echo_"";_fi := -Wno-restrict
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wmaybe-uninitialized_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-maybe-uninitialized";_else_echo_"";_fi := -Wno-maybe-uninitialized
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wno-maybe-uninitialized_-fno-strict-overflow_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-fno-strict-overflow";_else_echo_"";_fi := -fno-strict-overflow
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wno-maybe-uninitialized_-fno-strict-overflow_-fno-merge-all-constants_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-fno-merge-all-constants";_else_echo_"";_fi := -fno-merge-all-constants
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wno-maybe-uninitialized_-fno-strict-overflow_-fno-merge-all-constants_-fmerge-constants_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-fmerge-constants";_else_echo_"";_fi := -fmerge-constants
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wno-maybe-uninitialized_-fno-strict-overflow_-fno-merge-all-constants_-fmerge-constants_-fno-stack-check_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-fno-stack-check";_else_echo_"";_fi := -fno-stack-check
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wno-maybe-uninitialized_-fno-strict-overflow_-fno-merge-all-constants_-fmerge-constants_-fno-stack-check_-fconserve-stack_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-fconserve-stack";_else_echo_"";_fi := -fconserve-stack
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wno-maybe-uninitialized_-fno-strict-overflow_-fno-merge-all-constants_-fmerge-constants_-fno-stack-check_-fconserve-stack_-Werror_implicit-int_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Werror_implicit-int";_else_echo_"";_fi := -Werror=implicit-int
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wno-maybe-uninitialized_-fno-strict-overflow_-fno-merge-all-constants_-fmerge-constants_-fno-stack-check_-fconserve-stack_-Werror_implicit-int_-Werror_strict-prototypes_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Werror_strict-prototypes";_else_echo_"";_fi := -Werror=strict-prototypes
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wno-maybe-uninitialized_-fno-strict-overflow_-fno-merge-all-constants_-fmerge-constants_-fno-stack-check_-fconserve-stack_-Werror_implicit-int_-Werror_strict-prototypes_-Werror_date-time_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Werror_date-time";_else_echo_"";_fi := -Werror=date-time
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wno-maybe-uninitialized_-fno-strict-overflow_-fno-merge-all-constants_-fmerge-constants_-fno-stack-check_-fconserve-stack_-Werror_implicit-int_-Werror_strict-prototypes_-Werror_date-time_-Werror_incompatible-pointer-types_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Werror_incompatible-pointer-types";_else_echo_"";_fi := -Werror=incompatible-pointer-types
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wno-maybe-uninitialized_-fno-strict-overflow_-fno-merge-all-constants_-fmerge-constants_-fno-stack-check_-fconserve-stack_-Werror_implicit-int_-Werror_strict-prototypes_-Werror_date-time_-Werror_incompatible-pointer-types_-Werror_designated-init_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Werror_designated-init";_else_echo_"";_fi := -Werror=designated-init
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if____gcc_-Werror__-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wno-maybe-uninitialized_-fno-strict-overflow_-fno-merge-all-constants_-fmerge-constants_-fno-stack-check_-fconserve-stack_-Werror_implicit-int_-Werror_strict-prototypes_-Werror_date-time_-Werror_incompatible-pointer-types_-Werror_designated-init_-fmacro-prefix-map_./__-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-fmacro-prefix-map_./_";_else_echo_"";_fi := 
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___ar_rcD_"_TMP"__>/dev/null_2>&1;_then_echo_"D";_else_echo_"";_fi := D
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc_-Werror_-D__KERNEL___-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wno-maybe-uninitialized_-fno-strict-overflow_-fno-merge-all-constants_-fmerge-constants_-fno-stack-check_-fconserve-stack_-Werror_implicit-int_-Werror_strict-prototypes_-Werror_date-time_-Werror_incompatible-pointer-types_-Werror_designated-init_-Wpacked-not-aligned_-c_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"-Wno-packed-not-aligned";_else_echo_"";_fi := 
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___gcc__-Wl_--build-id_-D__KERNEL_____-Wall_-Wundef_-Wstrict-prototypes_-Wno-trigraphs_-fno-strict-aliasing_-fno-common_-fshort-wchar_-Werror-implicit-function-declaration_-Werror_return-type_-Wno-format-security_-std_gnu89_-fno-PIE_-mno-sse_-mno-mmx_-mno-sse2_-mno-3dnow_-mno-avx_-m64_-falign-jumps_1_-falign-loops_1_-mno-80387_-mno-fp-ret-in-387_-mpreferred-stack-boundary_3_-mskip-rax-setup_-mtune_generic_-mno-red-zone_-mcmodel_kernel_-funit-at-a-time_-DCONFIG_X86_X32_ABI_-DCONFIG_AS_CFI_1_-DCONFIG_AS_CFI_SIGNAL_FRAME_1_-DCONFIG_AS_CFI_SECTIONS_1_-DCONFIG_AS_FXSAVEQ_1_-DCONFIG_AS_SSSE3_1_-DCONFIG_AS_CRC32_1_-DCONFIG_AS_AVX_1_-DCONFIG_AS_AVX2_1_-DCONFIG_AS_AVX512_1_-DCONFIG_AS_SHA1_NI_1_-DCONFIG_AS_SHA256_NI_1_-pipe_-Wno-sign-compare_-fno-asynchronous-unwind-tables_-mindirect-branch_thunk-extern_-mindirect-branch-register_-fno-jump-tables_-fno-delete-null-pointer-checks_-Wno-frame-address_-Wno-format-truncation_-Wno-format-overflow_-Wno-int-in-bool-context_-O2_--param_allow-store-data-races_0_-DCC_HAVE_ASM_GOTO_-Wframe-larger-than_1024_-fstack-protector-strong_-Wno-unused-but-set-variable_-Wno-unused-const-variable_-fno-omit-frame-pointer_-fno-optimize-sibling-calls_-fno-var-tracking-assignments_-g_-gdwarf-4_-pg_-mrecord-mcount_-mfentry_-DCC_USING_FENTRY_-Wdeclaration-after-statement_-Wno-pointer-sign_-Wno-array-bounds_-Wno-stringop-overflow_-Wno-restrict_-Wno-maybe-uninitialized_-fno-strict-overflow_-fno-merge-all-constants_-fmerge-constants_-fno-stack-check_-fconserve-stack_-Werror_implicit-int_-Werror_strict-prototypes_-Werror_date-time_-Werror_incompatible-pointer-types_-Werror_designated-init_-nostdlib_-x_c_/dev/null_-o_"_TMP"__>/dev/null_2>&1;_then_echo_"_-Wl_--build-id";_else_echo_"";_fi := -Wl,--build-id
__cached_set_-e;_TMP_/home/<USER>/18085373-assignment/task3/.tmp___/tmp;_TMPO_/home/<USER>/18085373-assignment/task3/.tmp___/tmp.o;_mkdir_-p_/home/<USER>/18085373-assignment/task3/.tmp___;_trap_"rm_-rf_/home/<USER>/18085373-assignment/task3/.tmp___"_EXIT;_if___ld_-m_elf_x86_64_-z_max-page-size_0x200000_-z_noexecstack_--no-warn-rwx-segments_-v__>/dev/null_2>&1;_then_echo_"--no-warn-rwx-segments";_else_echo_"";_fi := 
__cached_/bin/bash_./scripts/gcc-version.sh_gcc := 0705
